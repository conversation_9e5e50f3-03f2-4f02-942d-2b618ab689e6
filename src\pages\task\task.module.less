.modal-operation {
  text-align: right;
  margin-top: 20px;
}

.add-task-moduel {
  :global(.ant-modal-body) {
    padding: 0 0 0 10px;
  }
}
.yth-inspection-moduel {
  position: relative;
  height: calc(100vh - 180px); // 弹窗高度：屏幕高度减去上下各50px留白
  overflow-y: auto; // 允许垂直滚动
  overflow-x: hidden; // 隐藏水平滚动
  padding-right: 10px; // 为滚动条留出空间
  box-sizing: border-box; // 确保padding包含在高度计算中
  margin-top: 10px;
}
.actual-end-time{
  margin: 10px 0;

}
/* 在task.module.less中 */
.custom-label {
  width: 40px !important;
  min-width: 40px;
}
