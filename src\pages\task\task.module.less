.modal-operation {
  text-align: right;
  margin-top: 20px;
}

.add-task-moduel {
  :global(.ant-modal-body) {
    padding: 0 0 0 10px;
  }
}
.yth-inspection-moduel {
  position: relative;
  height: calc(100vh - 180px); // 弹窗高度：屏幕高度减去上下各50px留白
  overflow-y: auto; // 允许垂直滚动
  overflow-x: hidden; // 隐藏水平滚动
  padding-right: 10px; // 为滚动条留出空间
  box-sizing: border-box; // 确保padding包含在高度计算中
  margin-top: 10px;
}
.actual-end-time{
  margin: 10px 0;

}
/* 在task.module.less中 */
.custom-label {
  width: 40px !important;
  min-width: 40px !important;
}

/* 全局覆盖YTHForm的label样式 */
:global(.yth-form-item-label) {
  &.custom-label-global {
    width: 30px !important;
    min-width: 30px !important;
    flex: 0 0 30px !important;
  }
}

/* 更强的选择器覆盖 */
:global(.yth-inspection-moduel) {
  :global(.custom-label-global) {
    :global(.yth-form-item-label),
    :global(.ant-form-item-label) {
      width: 30px !important;
      min-width: 30px !important;
      flex: 0 0 30px !important;
      max-width: 30px !important;
    }
  }
}

/* 直接针对特定表单项 */
:global(.yth-form-item) {
  &:global(.custom-label-global) {
    :global(.yth-form-item-label),
    :global(.ant-form-item-label) {
      width: 30px !important;
      min-width: 30px !important;
      flex: 0 0 30px !important;
      max-width: 30px !important;
    }
  }
}

/* 最强力的覆盖 - 如果以上都不行就用这个 */
:global(*) {
  &:global(.custom-label-global) {
    :global(label),
    :global(.yth-form-item-label),
    :global(.ant-form-item-label),
    :global(.ant-col),
    :global(.yth-col) {
      width: 30px !important;
      min-width: 30px !important;
      flex: 0 0 30px !important;
      max-width: 30px !important;
    }
  }
}
